<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
    </style>
</head>
<body>
    <h1>Browser Compatibility Diagnostic</h1>
    <p>This page tests various APIs to help diagnose Firefox compatibility issues.</p>
    
    <div id="browser-info" class="test info">
        <h3>Browser Information</h3>
        <p id="user-agent"></p>
        <p id="protocol"></p>
    </div>

    <div id="test-results"></div>
    
    <button onclick="runTests()">Run API Tests</button>

    <script>
        // Display browser information
        document.getElementById('user-agent').textContent = `User Agent: ${navigator.userAgent}`;
        document.getElementById('protocol').textContent = `Protocol: ${window.location.protocol}`;

        async function testAPI(name, url, transform = (data) => data) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test';
            resultDiv.innerHTML = `<h3>${name}</h3><p>Testing...</p>`;
            document.getElementById('test-results').appendChild(resultDiv);

            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const transformedData = transform(data);
                
                resultDiv.className = 'test success';
                resultDiv.innerHTML = `
                    <h3>${name} ✅</h3>
                    <p><strong>Status:</strong> Success</p>
                    <p><strong>IP:</strong> ${transformedData.ip || 'N/A'}</p>
                    <p><strong>Location:</strong> ${transformedData.city || 'N/A'}, ${transformedData.country_name || 'N/A'}</p>
                `;
            } catch (error) {
                resultDiv.className = 'test error';
                resultDiv.innerHTML = `
                    <h3>${name} ❌</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>Type:</strong> ${error.name}</p>
                `;
            }
        }

        async function runTests() {
            document.getElementById('test-results').innerHTML = '';
            
            // Test different APIs
            await testAPI('ipapi.co (HTTPS)', 'https://ipapi.co/json/');
            
            await testAPI('ip-api.com (HTTP)', 'http://ip-api.com/json/', (data) => ({
                ip: data.query,
                city: data.city,
                country_name: data.country
            }));
            
            await testAPI('ipify (HTTPS)', 'https://api.ipify.org?format=json');
            
            await testAPI('httpbin (HTTPS)', 'https://httpbin.org/ip', (data) => ({
                ip: data.origin,
                city: 'N/A',
                country_name: 'N/A'
            }));
        }

        // Auto-run tests on page load
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
