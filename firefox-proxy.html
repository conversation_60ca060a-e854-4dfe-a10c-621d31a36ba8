<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP Address Locator - Firefox Proxy</title>
    <link rel="stylesheet" href="style.css">
    <!-- Leaflet CSS for maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
</head>

<body>
    <div class="container">
        <h1>IP Address Locator (Firefox Proxy)</h1>
        <p>Enter an IP address to find its location. This version uses a local server proxy to bypass Firefox restrictions.</p>

        <form id="ip-form">
            <input type="text" id="ip-input" placeholder="e.g., *******" required>
            <button type="submit">Locate</button>
        </form>

        <div id="results" class="hidden">
            <h2>Location Details:</h2>
            <p id="ip-display"></p>
            <p id="city"></p>
            <p id="region"></p>
            <p id="country"></p>
            <p id="latitude-longitude"></p>
            <p id="isp"></p>
            <div id="map" style="height: 300px; margin-top: 20px;"></div> <!-- Map container -->
        </div>

        <div id="error" class="hidden error-message"></div>
    </div>

    <!-- Leaflet JS for maps -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Global variable to store the current map instance
        let currentMap = null;

        // Function to fetch and display IP data using local server proxy
        function fetchIPData(ip = null) {
            const resultsDiv = document.getElementById('results');
            const errorDiv = document.getElementById('error');
            const ipInput = document.getElementById('ip-input');

            // Hide previous results/error and show loading
            resultsDiv.classList.add('hidden');
            errorDiv.classList.add('hidden');
            errorDiv.textContent = 'Loading...';
            errorDiv.classList.remove('hidden');

            // Use local server proxy to avoid CORS issues
            let apiUrl = '/api/ip';
            if (ip) {
                apiUrl = `/api/ip?ip=${encodeURIComponent(ip)}`;
            }

            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    // Display text results
                    document.getElementById('ip-display').textContent = `IP: ${data.ip}`;
                    document.getElementById('city').textContent = `City: ${data.city || 'N/A'}`;
                    document.getElementById('region').textContent = `Region: ${data.region || 'N/A'}`;
                    document.getElementById('country').textContent = `Country: ${data.country_name || 'N/A'}`;
                    document.getElementById('latitude-longitude').textContent = `Lat/Long: ${data.latitude || 'N/A'}, ${data.longitude || 'N/A'}`;
                    document.getElementById('isp').textContent = `ISP: ${data.org || 'N/A'}`;

                    resultsDiv.classList.remove('hidden');
                    errorDiv.classList.add('hidden');

                    // Render map if lat/long available
                    if (data.latitude && data.longitude) {
                        renderMap(data.latitude, data.longitude, data.city);
                    }

                    // Pre-fill the input with the detected IP
                    ipInput.value = data.ip;
                })
                .catch(error => {
                    showError(`Error: ${error.message}. Make sure the local server is running.`);
                });
        }

        // Function to show error
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
        }

        // Function to render map with proper cleanup
        function renderMap(lat, lon, city) {
            const mapDiv = document.getElementById('map');
            
            // Remove existing map if it exists
            if (currentMap) {
                currentMap.remove();
                currentMap = null;
            }
            
            // Clear the map container
            mapDiv.innerHTML = '';

            // Create new map
            currentMap = L.map('map').setView([lat, lon], 10);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(currentMap);

            L.marker([lat, lon]).addTo(currentMap)
                .bindPopup(`Location: ${city || 'Unknown'}`)
                .openPopup();
        }

        // Auto-detect on page load
        window.addEventListener('load', function () {
            fetchIPData(); // Call without IP for auto-detect
        });

        // Handle form submission
        document.getElementById('ip-form').addEventListener('submit', function (event) {
            event.preventDefault();

            const ipInput = document.getElementById('ip-input').value.trim();

            if (!ipInput) {
                showError('Please enter a valid IP address.');
                return;
            }

            fetchIPData(ipInput);
        });
    </script>
</body>

</html>
