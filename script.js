// Function to fetch and display IP data (reused for both auto-detect and form submission)
function fetchIPData(ip = null) {
    const resultsDiv = document.getElementById('results');
    const errorDiv = document.getElementById('error');
    const ipInput = document.getElementById('ip-input');

    // Hide previous results/error and show loading
    resultsDiv.classList.add('hidden');
    errorDiv.classList.add('hidden');
    errorDiv.textContent = 'Loading...'; // Temporary loading message
    errorDiv.classList.remove('hidden');

    let apiUrl = 'https://ipapi.co/json/'; // Default for auto-detect
    if (ip) {
        apiUrl = `https://ipapi.co/${ip}/json/`;
    }

    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error('API error or invalid IP');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.reason || 'API error');
            }

            // Display text results
            document.getElementById('ip-display').textContent = `IP: ${data.ip}`;
            document.getElementById('city').textContent = `City: ${data.city || 'N/A'}`;
            document.getElementById('region').textContent = `Region: ${data.region || 'N/A'}`;
            document.getElementById('country').textContent = `Country: ${data.country_name || 'N/A'}`;
            document.getElementById('latitude-longitude').textContent = `Lat/Long: ${data.latitude}, ${data.longitude}`;
            document.getElementById('isp').textContent = `ISP: ${data.org || 'N/A'}`;

            resultsDiv.classList.remove('hidden');
            errorDiv.classList.add('hidden'); // Hide loading/error

            // Render map if lat/long available
            if (data.latitude && data.longitude) {
                renderMap(data.latitude, data.longitude, data.city);
            }

            // Pre-fill the input with the detected IP (for auto-detect or manual)
            ipInput.value = data.ip;
        })
        .catch(error => {
            showError(error.message);
        });
}

// Function to show error (unchanged)
function showError(message) {
    const errorDiv = document.getElementById('error');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
}

// Function to render map (unchanged)
function renderMap(lat, lon, city) {
    const mapDiv = document.getElementById('map');
    mapDiv.innerHTML = ''; // Clear previous map

    const map = L.map('map').setView([lat, lon], 10); // Zoom level 10 for city view

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    L.marker([lat, lon]).addTo(map)
        .bindPopup(`Location: ${city || 'Unknown'}`)
        .openPopup();
}

// Auto-detect on page load
window.addEventListener('load', function () {
    fetchIPData(); // Call without IP for auto-detect
});

// Handle form submission (updated to use fetchIPData)
document.getElementById('ip-form').addEventListener('submit', function (event) {
    event.preventDefault(); // Prevent page reload

    const ipInput = document.getElementById('ip-input').value.trim();

    if (!ipInput) {
        showError('Please enter a valid IP address.');
        return;
    }

    fetchIPData(ipInput); // Call with user-provided IP
});
