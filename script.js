// Function to fetch and display IP data (reused for both auto-detect and form submission)
function fetchIPData(ip = null) {
    const resultsDiv = document.getElementById('results');
    const errorDiv = document.getElementById('error');
    const ipInput = document.getElementById('ip-input');

    // Hide previous results/error and show loading
    resultsDiv.classList.add('hidden');
    errorDiv.classList.add('hidden');
    errorDiv.textContent = 'Loading...'; // Temporary loading message
    errorDiv.classList.remove('hidden');

    // Try ipapi.co first, fallback to ip-api.com if CORS issues
    let apiUrl = 'https://ipapi.co/json/'; // Default for auto-detect
    if (ip) {
        apiUrl = `https://ipapi.co/${ip}/json/`;
    }

    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error('API error or invalid IP');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.reason || 'API error');
            }

            // Display text results
            document.getElementById('ip-display').textContent = `IP: ${data.ip}`;
            document.getElementById('city').textContent = `City: ${data.city || 'N/A'}`;
            document.getElementById('region').textContent = `Region: ${data.region || 'N/A'}`;
            document.getElementById('country').textContent = `Country: ${data.country_name || 'N/A'}`;
            document.getElementById('latitude-longitude').textContent = `Lat/Long: ${data.latitude}, ${data.longitude}`;
            document.getElementById('isp').textContent = `ISP: ${data.org || 'N/A'}`;

            resultsDiv.classList.remove('hidden');
            errorDiv.classList.add('hidden'); // Hide loading/error

            // Render map if lat/long available
            if (data.latitude && data.longitude) {
                renderMap(data.latitude, data.longitude, data.city);
            }

            // Pre-fill the input with the detected IP (for auto-detect or manual)
            ipInput.value = data.ip;
        })
        .catch(error => {
            // If ipapi.co fails, try ip-api.com as fallback (CORS-friendly)
            console.log('ipapi.co failed, trying ip-api.com:', error.message);

            let fallbackUrl = 'http://ip-api.com/json/'; // Default for auto-detect
            if (ip) {
                fallbackUrl = `http://ip-api.com/json/${ip}`;
            }

            return fetch(fallbackUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Both APIs failed');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'fail') {
                        throw new Error(data.message || 'API error');
                    }

                    // Map ip-api.com response to ipapi.co format
                    const mappedData = {
                        ip: data.query,
                        city: data.city,
                        region: data.regionName,
                        country_name: data.country,
                        latitude: data.lat,
                        longitude: data.lon,
                        org: data.isp
                    };

                    // Display results using mapped data
                    document.getElementById('ip-display').textContent = `IP: ${mappedData.ip}`;
                    document.getElementById('city').textContent = `City: ${mappedData.city || 'N/A'}`;
                    document.getElementById('region').textContent = `Region: ${mappedData.region || 'N/A'}`;
                    document.getElementById('country').textContent = `Country: ${mappedData.country_name || 'N/A'}`;
                    document.getElementById('latitude-longitude').textContent = `Lat/Long: ${mappedData.latitude}, ${mappedData.longitude}`;
                    document.getElementById('isp').textContent = `ISP: ${mappedData.org || 'N/A'}`;

                    resultsDiv.classList.remove('hidden');
                    errorDiv.classList.add('hidden');

                    // Render map if lat/long available
                    if (mappedData.latitude && mappedData.longitude) {
                        renderMap(mappedData.latitude, mappedData.longitude, mappedData.city);
                    }

                    // Pre-fill the input with the detected IP
                    ipInput.value = mappedData.ip;
                })
                .catch(fallbackError => {
                    showError(`Network error: ${fallbackError.message}. Please check your internet connection or try using a local server.`);
                });
        });
}

// Function to show error (unchanged)
function showError(message) {
    const errorDiv = document.getElementById('error');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
}

// Function to render map (unchanged)
function renderMap(lat, lon, city) {
    const mapDiv = document.getElementById('map');
    mapDiv.innerHTML = ''; // Clear previous map

    const map = L.map('map').setView([lat, lon], 10); // Zoom level 10 for city view

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    L.marker([lat, lon]).addTo(map)
        .bindPopup(`Location: ${city || 'Unknown'}`)
        .openPopup();
}

// Auto-detect on page load
window.addEventListener('load', function () {
    fetchIPData(); // Call without IP for auto-detect
});

// Handle form submission (updated to use fetchIPData)
document.getElementById('ip-form').addEventListener('submit', function (event) {
    event.preventDefault(); // Prevent page reload

    const ipInput = document.getElementById('ip-input').value.trim();

    if (!ipInput) {
        showError('Please enter a valid IP address.');
        return;
    }

    fetchIPData(ipInput); // Call with user-provided IP
});
