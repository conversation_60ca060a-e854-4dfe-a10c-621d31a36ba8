// Function to fetch and display IP data (reused for both auto-detect and form submission)
function fetchIPData(ip = null) {
    const resultsDiv = document.getElementById('results');
    const errorDiv = document.getElementById('error');
    const ipInput = document.getElementById('ip-input');

    // Hide previous results/error and show loading
    resultsDiv.classList.add('hidden');
    errorDiv.classList.add('hidden');
    errorDiv.textContent = 'Loading...'; // Temporary loading message
    errorDiv.classList.remove('hidden');

    // Try multiple APIs for better Firefox compatibility
    tryMultipleAPIs(ip)
        .then(data => {
            // Display text results
            document.getElementById('ip-display').textContent = `IP: ${data.ip}`;
            document.getElementById('city').textContent = `City: ${data.city || 'N/A'}`;
            document.getElementById('region').textContent = `Region: ${data.region || 'N/A'}`;
            document.getElementById('country').textContent = `Country: ${data.country_name || 'N/A'}`;
            document.getElementById('latitude-longitude').textContent = `Lat/Long: ${data.latitude}, ${data.longitude}`;
            document.getElementById('isp').textContent = `ISP: ${data.org || 'N/A'}`;

            resultsDiv.classList.remove('hidden');
            errorDiv.classList.add('hidden'); // Hide loading/error

            // Render map if lat/long available
            if (data.latitude && data.longitude) {
                renderMap(data.latitude, data.longitude, data.city);
            }

            // Pre-fill the input with the detected IP (for auto-detect or manual)
            ipInput.value = data.ip;
        })
        .catch(error => {
            showError(`Network error: ${error.message}. Please check your internet connection or try using a local server.`);
        });
}

// Function to try multiple APIs for better browser compatibility
async function tryMultipleAPIs(ip = null) {
    const apis = [
        {
            name: 'ipapi.co',
            url: ip ? `https://ipapi.co/${ip}/json/` : 'https://ipapi.co/json/',
            transform: (data) => data // No transformation needed
        },
        {
            name: 'ip-api.com',
            url: ip ? `http://ip-api.com/json/${ip}` : 'http://ip-api.com/json/',
            transform: (data) => ({
                ip: data.query,
                city: data.city,
                region: data.regionName,
                country_name: data.country,
                latitude: data.lat,
                longitude: data.lon,
                org: data.isp
            })
        },
        {
            name: 'ipify + ipapi fallback',
            url: ip ? `https://geo.ipify.org/api/v2/country,city?apiKey=at_demo&ipAddress=${ip}` : 'https://api.ipify.org?format=json',
            transform: (data) => {
                if (data.location) {
                    // ipify response
                    return {
                        ip: data.ip,
                        city: data.location.city,
                        region: data.location.region,
                        country_name: data.location.country,
                        latitude: data.location.lat,
                        longitude: data.location.lng,
                        org: data.isp || 'N/A'
                    };
                } else {
                    // Simple ipify response (IP only)
                    return {
                        ip: data.ip,
                        city: 'N/A',
                        region: 'N/A',
                        country_name: 'N/A',
                        latitude: null,
                        longitude: null,
                        org: 'N/A'
                    };
                }
            }
        }
    ];

    for (const api of apis) {
        try {
            console.log(`Trying ${api.name}...`);
            const response = await fetch(api.url);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            if (data.error || data.status === 'fail') {
                throw new Error(data.reason || data.message || 'API error');
            }

            console.log(`${api.name} succeeded`);
            return api.transform(data);

        } catch (error) {
            console.log(`${api.name} failed:`, error.message);
            continue; // Try next API
        }
    }

    throw new Error('All APIs failed. Please check your internet connection or try using a local server.');
}

// Function to show error (unchanged)
function showError(message) {
    const errorDiv = document.getElementById('error');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
}

// Global variable to store the current map instance
let currentMap = null;

// Function to render map with proper cleanup
function renderMap(lat, lon, city) {
    const mapDiv = document.getElementById('map');

    // Remove existing map if it exists
    if (currentMap) {
        currentMap.remove();
        currentMap = null;
    }

    // Clear the map container
    mapDiv.innerHTML = '';

    // Create new map
    currentMap = L.map('map').setView([lat, lon], 10); // Zoom level 10 for city view

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(currentMap);

    L.marker([lat, lon]).addTo(currentMap)
        .bindPopup(`Location: ${city || 'Unknown'}`)
        .openPopup();
}

// Auto-detect on page load
window.addEventListener('load', function () {
    fetchIPData(); // Call without IP for auto-detect
});

// Handle form submission (updated to use fetchIPData)
document.getElementById('ip-form').addEventListener('submit', function (event) {
    event.preventDefault(); // Prevent page reload

    const ipInput = document.getElementById('ip-input').value.trim();

    if (!ipInput) {
        showError('Please enter a valid IP address.');
        return;
    }

    fetchIPData(ipInput); // Call with user-provided IP
});
