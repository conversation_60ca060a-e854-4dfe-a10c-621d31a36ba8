const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
const url = require('url');

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);

    // Handle API proxy requests
    if (parsedUrl.pathname === '/api/ip') {
        handleIPRequest(req, res, parsedUrl.query.ip);
        return;
    }

    // Get the file path
    let filePath = '.' + req.url;
    if (filePath === './') {
        filePath = './index.html';
    }

    // Get the file extension
    const extname = String(path.extname(filePath)).toLowerCase();

    // Set content type based on file extension
    const mimeTypes = {
        '.html': 'text/html',
        '.js': 'text/javascript',
        '.css': 'text/css',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.wav': 'audio/wav',
        '.mp4': 'video/mp4',
        '.woff': 'application/font-woff',
        '.ttf': 'application/font-ttf',
        '.eot': 'application/vnd.ms-fontobject',
        '.otf': 'application/font-otf',
        '.wasm': 'application/wasm'
    };

    const contentType = mimeTypes[extname] || 'application/octet-stream';

    // Read and serve the file
    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end('<h1>404 Not Found</h1>', 'utf-8');
            } else {
                res.writeHead(500);
                res.end(`Server Error: ${error.code}`, 'utf-8');
            }
        } else {
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(content, 'utf-8');
        }
    });
});

// Function to handle IP geolocation requests through server proxy
function handleIPRequest(req, res, targetIP) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    res.setHeader('Content-Type', 'application/json');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    const apis = [
        {
            name: 'ipapi.co',
            url: targetIP ? `https://ipapi.co/${targetIP}/json/` : 'https://ipapi.co/json/',
            transform: (data) => data
        },
        {
            name: 'ip-api.com',
            url: targetIP ? `http://ip-api.com/json/${targetIP}` : 'http://ip-api.com/json/',
            transform: (data) => ({
                ip: data.query,
                city: data.city,
                region: data.regionName,
                country_name: data.country,
                latitude: data.lat,
                longitude: data.lon,
                org: data.isp
            })
        }
    ];

    tryAPIs(apis, 0, res);
}

function tryAPIs(apis, index, res) {
    if (res.headersSent) {
        return; // Response already sent
    }

    if (index >= apis.length) {
        res.writeHead(500);
        res.end(JSON.stringify({ error: 'All APIs failed' }));
        return;
    }

    const api = apis[index];
    const isHttps = api.url.startsWith('https://');
    const httpModule = isHttps ? https : http;

    console.log(`Trying ${api.name}...`);

    const request = httpModule.get(api.url, (apiRes) => {
        let data = '';

        apiRes.on('data', (chunk) => {
            data += chunk;
        });

        apiRes.on('end', () => {
            if (res.headersSent) return;

            try {
                const jsonData = JSON.parse(data);

                if (jsonData.error || jsonData.status === 'fail') {
                    console.log(`${api.name} returned error:`, jsonData.error || jsonData.message);
                    tryAPIs(apis, index + 1, res);
                    return;
                }

                const transformedData = api.transform(jsonData);
                console.log(`${api.name} succeeded`);

                res.writeHead(200);
                res.end(JSON.stringify(transformedData));
            } catch (error) {
                console.log(`${api.name} JSON parse error:`, error.message);
                tryAPIs(apis, index + 1, res);
            }
        });
    });

    request.on('error', (error) => {
        console.log(`${api.name} request error:`, error.message);
        if (!res.headersSent) {
            tryAPIs(apis, index + 1, res);
        }
    });

    request.setTimeout(5000, () => {
        console.log(`${api.name} timeout`);
        request.destroy();
        if (!res.headersSent) {
            tryAPIs(apis, index + 1, res);
        }
    });
}

const PORT = 3000;
server.listen(PORT, () => {
    console.log(`Server running at http://localhost:${PORT}/`);
    console.log(`Server running at http://127.0.0.1:${PORT}/`);
});
